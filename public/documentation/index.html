<!doctype html>
<!--[if IE 6 ]><html lang="en-us" class="ie6"> <![endif]-->
<!--[if IE 7 ]><html lang="en-us" class="ie7"> <![endif]-->
<!--[if IE 8 ]><html lang="en-us" class="ie8"> <![endif]-->
<!--[if (gt IE 7)|!(IE)]><!-->
<html lang="en-us">
<!--<![endif]-->
<head>
<meta charset="utf-8">
<title>Logistic</title>
<meta name="description" content="Logistic">
<meta name="author" content="Templines">
<meta name="copyright" content="Templines">
<meta name="generator" content="Documenter v2.0 http://rxa.li/documenter">
<meta name="date" content="2015-09-16T00:00:00+02:00">
<link rel="stylesheet" href="assets/css/documenter_style.css" media="all">
<link rel="shortcut icon" type="image/x-icon" href="favicon.ico" />
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
<script src="assets/js/jquery.js"></script>
<script src="assets/js/jquery.scrollTo.js"></script>
<script src="assets/js/jquery.easing.js"></script>
<script>document.createElement('section');var duration='500',easing='swing';</script>
<script src="assets/js/script.js"></script>
</head>
<body class="documenter-project-Logistic-multipurpose-wordpress-template">
<div id="documenter_sidebar"> <a href="#documenter_cover" id="documenter_logo"></a>
  <ul id="documenter_nav">
    <li><a class="current" href="#section01">HOME PAGE SECTIONS</a></li>
    <li><a class="current" href="#section02">COLOR SWITCHER</a></li>
    <li><a class="current" href="#section03">PAGE PRELOADING EFFECT</a></li>
    <li><a class="current" href="#section04">HOME PAGE SLIDER</a></li>
    <li><a class="current" href="#section05">OWL CAROUSEL</a></li>
    <li><a class="current" href="#section05">MAIN STYLES AND SCRIPTS</a></li>
  </ul>
</div>
<div id="documenter_content">
<section id="documenter_cover">
  <h1>24/7 Express Logistics Services</h1>
  <div id="documenter_buttons"> <a href="http://support.templines.com/" class="btn btn-primary btn-large">Get support</a> <a href="http://html.templines.com/logistics/" class="btn btn-primary btn-large">Demo page</a> <a href="http://themeforest.net/user/templines" class="btn btn-primary btn-large">Buy Logistic</a> </div>
  <hr>
  <p><img src="assets/images/preview.jpg"></p>
</section>
<section id="documentation" class="documentation-row">
<div class="container">
<div class="row">
  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 clearfix">
    <p>Thank you for purchasing this item. We provide support for all of our Items at <a href="http://templines.com/" target="_blank">http://templines.com/</a>, but please make sure you read this documentation first. Thank you so much!</p>
    <hr>
    <p class="alert alert-success">If you are not familiar with HTML and CSS, I highly recommend that you to hire a specialist. It will save your time and you will get only positive emotions while using our product.</p>
    <p>Let's look at the structure of Landing Page. It includes its own sections with content. On request you can add or remove sections of the site. Please, be careful when removing unwanted sections. It can cause errors in <strong>custom.js</strong>. All tags have to be closed properly.</p>
    <h3>Tools to help work:</h3>
    <hr>
    <ul>
      <li><a target="_blank" href="https://addons.mozilla.org/ru/firefox/addon/firebug/">Firebug</a></li>
      <li class="li-last"><a target="_blank" href="https://developer.chrome.com/devtools/index">Chrome devtools</a></li>
    </ul>
    <div class="doc-section" id="section01">
      <h3>Home Page Sections</h3>
      <hr>
      <h4>section simple</h4>
      <pre>
<code data-language="html">


<section class="section_mod-a section_mod-d">
        <div class="container">
          <div class="row">
            <div class="col-xs-12">
            
            CONTENT
            
                </div>
              </div>
           </div>
  </section>



</code>
</pre>
      <h4>section with  background image</h4>
      <pre>
<code data-language="html">

<style>

.section-banner-1:after {
  background: url('media/bg/texture_wood-1.jpg');
}

</style>

<div class="section-banner-1 ">
        <div class="container">
          <div class="row">
            <div class="col-xs-12">
            
            CONTENT
            
                 </div>
              </div>
           </div>
</div>



</code>
</pre>
      <h4>section with  top border</h4>
      <pre>
<code data-language="html">



      <section class="section_mod-d border-top">
        
          <div class="container">
            <div class="row">
              <div class="col-xs-12">
            
            CONTENT
          
                 </div>
              </div>
           </div>
           
  </section>



</code>
</pre>
      <h4>section with  background color</h4>
      <pre>
<code data-language="html">

<style>

.section-banner {
  background-color: red;
}

</style>

<div class="section-banner-1 ">
        <div class="container">
          <div class="row">
            <div class="col-xs-12">
            
            CONTENT
            
                 </div>
              </div>
           </div>
</div>



</code>
</pre>
    </div>
    <div class="doc-section" id="section02">
      <h3>Color switcher</h3>
      <hr>

      <p>Choose a color scheme. Paste this code in HEAD</p>
      <pre>
<code data-language="html">

<link href="assets/plugins/switcher/css/color3.css"  media="all">


</code>
</pre>
    </div>
    <div class="doc-section" id="section03">
      <h3>Page preloading effect</h3>
      <hr>
      <p>It effect makes waiting process while page loading less boring for the user. Please just use this code .</p>
      <pre>
<code data-language="html">
<!-- Loader -->
<div id="page-preloader"><span class="spinner"></span></div>
<!-- Loader end -->
</code>
</pre>
    </div>
    <div class="doc-section" id="section04">
      <h3>Home page slider</h3>
      <hr>
      <p class="source-links"> <i class="fa fa-external-link-square"></i> <a target="_blank" href="http://iprodev.com/iview/">developer website </a> </p>
      <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/plugins/iview/ </p>
      <pre>
<code data-language="html">


<div id="iview" class="main-slider">

  <!--First Slide-->
   <div class="main-slider__item"
						data-iview:thumbnail="assets/media/main-slider/1.jpg" 
						data-iview:image="assets/media/main-slider/1.jpg" 
						data-iview:transition="block-drop-random" >
   </div>
   <!--First Slide-->

   <!--Second Slide-->
      <div class="main-slider__item"
						data-iview:thumbnail="assets/media/main-slider/1.jpg"
						data-iview:image="assets/media/main-slider/1.jpg"
						data-iview:transition="block-drop-random" >
        
      </div>
   <!--Second Slide-->
 
    </div>
      
      
</code>
</pre>
    </div>
    <div class="doc-section" id="section05">
      <h3>Owl carousel</h3>
      <hr>
      <p class="source-links"> <i class="fa fa-external-link-square"></i> <a target="_blank" href="http://owlgraphic.com/owlcarousel/">developer website </a> </p>
      <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/plugins/owl-carousel/ </p>
      <pre>
<code data-language="html">


<div class="main-slider slider-pro" id="my-slider">
      <div class="sp-slides"> 
        
        <!-- Slide 1 -->
        <div class="sp-slide">
        
         <img class="sp-image" src="assets/media/main-slider/1.jpg" height="650" width="1600" alt="logo">

        </div>
        
        <!-- Slide 2 -->
          <div class="sp-slide">
        
         <img class="sp-image" src="assets/media/main-slider/1.jpg" height="650" width="1600" alt="logo">

        </div>
        
        <!-- Slide 3 -->
          <div class="sp-slide">
        
         <img class="sp-image" src="assets/media/main-slider/1.jpg" height="650" width="1600" alt="logo">

        </div>
      </div>
      <!-- end sp-slides --> 
    </div>


    

      
      
</code>
</pre>
    </div>
    <div class="doc-section" id="section06">
      <h3>Main Styles and Scripts</h3>
      <div class="styles-box"> <strong> Master  Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/master.css</p>
        <strong> Global Theme Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/theme.css</p>
        <strong>Blog Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/blog.css</p>
        <strong> Responsive Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/responsive.css</p>
        <strong> Section Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/sections.css</p>
        <strong> Sidebar Styles</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/css/sidebar.css</p>
        
        <strong>Theme js</strong> <strong>
        <p class="</strong>pre customBgColor"> <i class="fa fa-folder-open"></i> assets/js/custom.js</p>
        <strong> Icons Fonts</strong>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i> assets/fonts/autoicon<br>
        </p>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i>assets/ fonts/flaticon<br>
        </p>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i>assets/ fonts/font-awesome<br>
        </p>
        <p class="pre customBgColor"> <i class="fa fa-folder-open"></i>assets/ fonts/simple<br>
        </p>
        </strong> </div>
      <section id="section14">
        <div class="page-header">
          <h3>Changelogs</h3>
          <hr class="notop">
        </div>
        <p>Changelogs file <a href="changelog.txt">changelog.txt</a></p>
      </section>
    </div>
  </div>
</div>
</section>

<!-- Syntax Highlighting -->
<link href="assets/rainbow/css/github.css" rel="stylesheet" type="text/css" media="screen">
<script src="assets/rainbow/js/rainbow.js"></script> 
<script src="assets/rainbow/js/language/generic.js"></script> 
<script src="assets/rainbow/js/language/html.js"></script> 
<script src="assets/rainbow/js/language/css.js"></script> 
<script src="assets/rainbow/js/language/php.js"></script> 
<script src="assets/rainbow/js/language/javascript.js"></script>
</body>
</html>