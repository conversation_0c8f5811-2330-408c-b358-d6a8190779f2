function validateEmail(email) {
  const re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
}

// Show/hide loading spinner
function showLoading(show) {
  if (show) {
    if ($("#loading-spinner").length === 0) {
      $("body").append('<div id="loading-spinner" style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(255,255,255,0.7);z-index:9999;display:flex;align-items:center;justify-content:center;"><div style="padding:20px;background:#fff;border-radius:8px;box-shadow:0 0 10px #ccc;">Sending...</div></div>');
    }
  } else {
    $("#loading-spinner").remove();
  }
}

function sendMailWithRetry(data, retries) {
  showLoading(true);
  $.post("https://entreship-api-21b709f4f6f8.herokuapp.com/sendEmail", data)
    .done(function() {
      showLoading(false);
      $("#mail-alert").css("display","block");
      $("#name").val("")
      $("#phone").val("")
      $("#email").val("")
      $("#company").val("")
      $("#business").val("")
      $("#enquiry_type").val("")
      $("#message").val("")
      alert( "Mail sent successfully!" );
      setTimeout(function() {
        location.reload();
      }, 1000);
    })
    .fail(function() {
      if (retries > 0) {
        setTimeout(function() {
          sendMailWithRetry(data, retries - 1);
        }, 3000); // wait 3 seconds before retry
      } else {
        showLoading(false);
        alert( "Something has gone wrong, please try again in a few seconds." );
      }
    });
}

$("#btn-sendmail").click(function(){
  var name = $("#name").val();
  var phone = $("#phone").val();
  var email = $("#email").val();
  var company = $("#company").val();
  var business = $("#business").val();
  var enquiry_type = $("#enquiry_type").val();
  var message = $("#message").val();

  var recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert("Please complete the CAPTCHA.");
    return;
  }
  if(name && phone && email && company && business && enquiry_type && message){
    if (validateEmail(email)) {
      var data = { 
        params: {
          name,
          phone,
          email,
          company,
          business,
          enquiry_type,
          message,
          key: 'mvs',
          recaptcha: recaptchaResponse,
          mailTo: ["<EMAIL>", "<EMAIL>"]
        }
      };
      sendMailWithRetry(data, 3); // try up to 3 times
    } else {
      alert(email + " is not valid Email! :(");
    }
  }else{
    alert("Input is required!")
  }
});