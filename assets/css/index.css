/*------------------------------------------------------------------

[Table of contents]

 - Preview page styles

-------------------------------------------------------------------*/

html .sp-body {
	background: #fff;
}

.paralax {
	background-attachment: fixed !important;
	background-repeat: no-repeat;
	background-position: 50% 0;
}

.demopage-header {
	text-align: center;
}
.demopage-header .section__inner {
	padding-top: 45px;
	padding-bottom: 40px;
}
.demopage-logo {
	display: block;
	margin: 0 auto;
	width: 383px;
}
.demopage-logo img {
	   margin: 0 auto;
	float: none;
	display: block;
}
.demopage-header h1 {
	   font-size: 18px;
	letter-spacing: 6px;
	line-height: 100%;
	padding-bottom: 45px;
	padding-top: 45px;
	text-align: center;
	width: 100%;
	color: #fff;
}
.demopage-header .button-border {
	border-color: #fff;
	clear: both;
	color: #fff;
	display: block;
	font-size: 15px;
	line-height: 100%;
	margin: auto;
	padding: 19px 0;
	text-align: center;
	width: 200px;
}

.demopage .hvr-rectangle-out::before {
	background: #e5534c none repeat scroll 0 0;
}
.demopage .hvr-rectangle-out:hover {
	border-color: #e5534c;
	color: #fff;
}
.demopage-content {
	padding: 92px 0 0;
}
.demopage-title {
	font-size: 25px;
	line-height: 100%;
	text-align: center;
	width: 100%;
	margin-bottom: 70px;
}
.demopage .starSeparator {
	color: #e5534c !important;
}
.demopage-preview {
	padding: 0px 0px 0;
}
.demopage-preview > div {
	padding-bottom: 70px;
}
.demopage-preview_item {
	display: block;
	height: 314px;
	margin: 0 auto;
	padding: 17px 18px 86px 16px;
	width: 370px;
	background: url("../media/demo-page/mac.png") no-repeat;
	background-size: contain;
	position: relative;
	z-index: 10;
}
.demopage-preview_inner {
	height: 100%;
	width: 100%;
	position: relative;
	display: block;
	overflow: hidden;
}
.demopage-preview_inner:after {
	content: '';
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	transition: all .1s;
}
.demopage-preview_inner:hover:after {
	box-shadow: inset 0 0 11px 2px white;
}
.demopage-preview_inner img {
	left: 0;
	position: absolute;
	top: 0;
	transition: all 4s ease 0s;
	width: 100%;
}
.demopage-preview h3 {
	font-size: 16px;
	line-height: 100%;
	padding: 0px 0 29px0;
	text-align: center;
	text-transform: uppercase;
}
.demopage-preview .button-border {
	border-color: #e5534c;
	font-size: 11px;
	padding: 13px 50px 12px;
}
.demopage-content .starSeparatorBox {
	padding-bottom: 25px;
}
.demopage-buynow {
	border-top: 2px solid #ececec;
	padding-bottom: 6px;
	padding-top: 70px;
	text-align: center;
}
.demopage-buynow .btn-primary {
	background-color: #e5534c;
	font-size: 15px;
	padding: 17px 68px 19px;
}
.demopage-buynow .btn-primary:hover {
	padding: 17px 75px 19px;
}
.demopage-helptitle {
	background-color: #f6f6f6;
	padding: 65px 0;
}
.demopage-helptitle h3 {
	font-size: 17px;
	text-align: center;
}
ul.demopage-help_list {
	float: left;
	padding: 0 19%;
	width: 100%;
	text-align: center;
}
ul.demopage-help_list li:first-child {
	border-left: 1px solid #eee;
}
ul.demopage-help_list li {
	border-right: 1px solid #eee;
display: inline-block;
}
ul.demopage-help_list li a {
	color: #333;
	float: left;
	font-size: 16px;
	line-height: 100%;
	padding: 70px 37px 65px;
	text-decoration: none;
	text-transform: uppercase;
}
ul.demopage-help_list li a span {
	color: #e5534c;
	float: left;
	font-size: 33px;
	margin: -8px 15px 0 0;
}
ul.demopage-help_list li a:hover {
	color: #e5534c;
}
.demopage-footer {
	background-color: #333;
	padding: 46px 0;
}
.demopage-copyright {
	float: left;
	font-size: 13px;
	line-height: 100%;
	text-align: center;
	width: 100%;
}
.demopage-copyright span {
	color: #e5534c;
	margin: 0 4px;
}

.demopage-help_list{
	list-style:none;
	margin:0;
}

.demopage-copyright{
	color:#fff;
}
@media (max-width: 1199px) {
	.demopage-preview {
		padding: 81px 40px 0;
	}
	.demopage-preview_item {
		width: 270px;
		height: 230px;
		padding: 13px 14px 64px 13px;
	}
	.demopage-title {margin-bottom: 0;}
	ul.demopage-help_list {
		padding: 0;
	}
	ul.demopage-help_list li {
		width: 100%;
	}
	ul.demopage-help_list li a {
		text-align: center;
		width: 100%;
	}
	ul.demopage-help_list li {
		border-left: 1px solid #eee;
	}
	ul.demopage-help_list li:first-child {
		border-bottom: 1px solid #eee;
	}
}
@media (max-width: 991px) {
	.demopage-preview {
		padding: 81px 0 0;
	}
}
