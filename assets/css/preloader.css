.preloaderjs .spinner{
	display:none !important;
}

.preloaderjs#page-preloader{
	background:  rgba( 46, 46, 46, 0.99)  !important;
}


#page-preloader {
	position: fixed;
	z-index: 100500;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #2e2e2e;
}
#page-preloader .spinner {
	position: absolute;
	z-index: 1001;
	top: 50%;
	left: 50%;
	display: block;
	width: 100px;
	height: 100px;
	margin-top: -50px;
	margin-left: -50px;
	-webkit-animation: spin 2.5s infinite linear;
	animation: spin 2.5s infinite linear;
	border: 3px solid transparent;
	border-top-color: #e7e4d7;
	border-radius: 50%;
}
#page-preloader .spinner:before, #page-preloader .spinner:after {
	position: absolute;
	content: '';
	border-radius: 50%;
}
#page-preloader .spinner:before {
	top: 5px;
	right: 5px;
	bottom: 5px;
	left: 5px;
	-webkit-animation: spin 2s infinite linear;
	animation: spin 2s infinite linear;
	border: 3px solid transparent;
	border-top-color:#71383e;
}
#page-preloader .spinner:after {
	top: 15px;
	right: 15px;
	bottom: 15px;
	left: 15px;
	-webkit-animation: spin 1s infinite linear;
	animation: spin 1s infinite linear;
	border: 3px solid transparent;
	border-top-color: #efa96b;
}
@-webkit-keyframes spin {
 0% {
 -webkit-transform: rotate(0);
transform: rotate(0);
}
100% {
 -webkit-transform: rotate(360deg);
transform: rotate(360deg);
}
}
@keyframes spin {
 0% {
 -webkit-transform: rotate(0);
transform: rotate(0);
}
100% {
 -webkit-transform: rotate(360deg);
transform: rotate(360deg);
}
}
