.relative {
    position: relative !important;
}

.text-white {
    color: white;
}

.service-content {
	position: absolute;
    top: 46%;
    left: 0;
    right: 0;
    transform: translate(0, -50%);
    z-index: 2;
    padding: 0 10%;
}

.img-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1;
}

.contact-row {
  display: flex;
  gap: 32px;
  align-items: stretch;
  flex-wrap: wrap;
}
.contact-box {
  background: #f9f9f9;
  padding: 24px;
  border-radius: 16px;
  height: 100%;
  flex: 1 1 320px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.custom-carousel {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: auto;
  overflow: hidden;
}
.carousel-img {
  display: none;
  width: 100%;
  transition: opacity 0.5s;
}
.carousel-img.active {
  display: block;
}
.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: #222;
  color: #fff;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  opacity: 0.7;
  font-size: 18px;
  z-index: 2;
}
.carousel-btn.prev { left: 10px; }
.carousel-btn.next { right: 10px; }

/* Service Division Cards Styles */
.service-division-card {
  position: relative;
  height: 300px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-division-card:hover {
  transform: translateY(-5px);
  /* box-shadow: 0 8px 25px rgba(0,0,0,0.15); */
}

.service-division-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-division-card:hover .service-division-img {
  transform: scale(1.05);
}

.service-division-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255,.85);
  color: white;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-division-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.service-division-icon svg {
  color: white;
}

.service-division-content {
  flex: 1;
  color: black;
}
.service-division-content p {
    font-size: 14px;
    font-weight: 500;
}

.service-division-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  line-height: 1.2;
}

.about-list li {
  margin-bottom: 8px;
}