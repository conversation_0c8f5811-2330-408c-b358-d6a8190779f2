/*
 * Translated default messages for bootstrap-select.
 * Locale: SL (Slovenian)
 * Region: SI (Slovenia)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: '<PERSON><PERSON> izbranega',
    noneResultsText: '<PERSON> za {0}',
    countSelectedText: function (numSelected, numTotal) {
      "Število izbranih: {0}";
    },
    maxOptionsText: function (numAll, numGroup) {
      return [
        'Omejitev dosežena (max. izbranih: {n})',
        'Omejitev skupine dosežena (max. izbranih: {n})'
      ];
    },
    selectAllText: 'Izberi vse',
    deselectAllText: 'Počisti izbor',
    multipleSeparator: ', '
  };
})(jQuery);
