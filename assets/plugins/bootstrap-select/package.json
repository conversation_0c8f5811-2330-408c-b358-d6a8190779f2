{"name": "bootstrap-select", "title": "bootstrap-select", "main": "dist/js/bootstrap-select.js", "description": "A custom <select> for Bootstrap using button dropdown as replacement", "version": "1.7.2", "homepage": "http://silviomoreto.github.io/bootstrap-select", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/silviomoreto"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/silviomoreto"}, {"name": "Ana Carolina", "url": "https://github.com/anacarolinats"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/caseyjhol"}, {"name": "<PERSON>", "url": "https://github.com/mattb<PERSON>son"}, {"name": "t0xicCode", "url": "https://github.com/t0xicCode"}], "repository": {"type": "git", "url": "git://github.com/silviomoreto/bootstrap-select.git"}, "license": {"type": "MIT", "url": "https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE"}, "dependencies": {"jquery": ">=1.8"}, "devDependencies": {"grunt": "~0.4.5", "grunt-autoprefixer": "~1.0.0", "grunt-banner": "~0.3.1", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-compress": "~0.13.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-csslint": "~0.2.0", "grunt-contrib-cssmin": "~0.11.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-less": "~0.11.3", "grunt-contrib-uglify": "~0.7.0", "grunt-contrib-watch": "~0.6.1", "grunt-sed": "~0.1.1", "grunt-umd": "~2.3.3", "load-grunt-tasks": "~2.0.0"}, "keywords": ["form", "bootstrap", "select", "replacement"]}