/*!
 * Bootstrap-select v1.7.2 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2015 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Aucune s&eacute;lection",noneResultsText:"Aucun r&eacute;sultat pour {0}",countSelectedText:function(a,b){return a>1?"{0} &eacute;l&eacute;ments s&eacute;lectionn&eacute;s":"{0} &eacute;l&eacute;ment s&eacute;lectionn&eacute;"},maxOptionsText:function(a,b){return[a>1?"Limite atteinte ({n} &eacute;l&eacute;ments max)":"Limite atteinte ({n} &eacute;l&eacute;ment max)",b>1?"Limite du groupe atteinte ({n} &eacute;l&eacute;ments max)":"Limite du groupe atteinte ({n} &eacute;l&eacute;ment max)"]},multipleSeparator:", "}}(jQuery)});