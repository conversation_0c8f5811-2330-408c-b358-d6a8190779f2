{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Lightweight plugin to render simple, animated and retina optimized pie charts", "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}, {"type": "GPL", "url": "http://www.opensource.org/licenses/gpl-license.php"}], "version": "2.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://robert-fleischmann.de"}, "scripts": {"test": "grunt karma:ci"}, "repository": {"type": "git", "url": "https://github.com/rendro/easy-pie-chart.git"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-concat": "~0.4.0", "grunt-banner": "~0.2.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-watch": "~0.6.1", "grunt-contrib-less": "~0.11.0", "grunt-contrib-jshint": "~0.10.0", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.0", "karma-chrome-launcher": "~0.1.0", "karma-html2js-preprocessor": "~0.1.0", "karma-jasmine": "~0.1.3", "karma-requirejs": "~0.2.1", "karma-coffee-preprocessor": "~0.2.1", "karma-phantomjs-launcher": "~0.1.0", "karma": "~0.12.14", "grunt-karma": "~0.8.3", "grunt-umd": "~1.7.3", "requirejs": "~2.1.10", "grunt-readme": "~0.4.5", "load-grunt-tasks": "~0.4.0"}}