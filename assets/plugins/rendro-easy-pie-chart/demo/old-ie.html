<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8"/>
	<title>jQuery</title>
	<meta name="viewport" content="width=device-width"/>
	<link rel="stylesheet" href="style.css"/>
	<script src="excanvas.compiled.js"></script>
	<script>
	// IE function.bind polyfill
	if (!Function.prototype.bind) {
		Function.prototype.bind = function (oThis) {
			if (typeof this !== "function") {
				// closest thing possible to the ECMAScript 5 internal IsCallable function
				throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");
			}

			var aArgs = Array.prototype.slice.call(arguments, 1),
				fToBind = this,
				fNOP = function () {},
				fBound = function () {
					return fToBind.apply(this instanceof fNOP && oThis ? this : oThis,
				aArgs.concat(Array.prototype.slice.call(arguments)));
			};

			fNOP.prototype = this.prototype;
			fBound.prototype = new fNOP();

			return fBound;
		};
	}
	</script>
</head>
<body>

	<ul>
		<li><a href="index.html">Vanilla JS</a></li>
		<li><a href="jquery.html">jQuery plugin</a></li>
		<li><a href="angular.html">Angular module</a></li>
	</ul>

	<p>Demo for old Internet Explorer Versions 7 and 8 with <a href="http://excanvas.sourceforge.net" target="_blank">excanvas</a> and a Function.bind polyfill.</p>

	<span class="chart" data-percent="86">
		<span class="percent"></span>
	</span>

	<span class="btn js_update">Update chart</span>

	<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>
	<script src="../dist/jquery.easypiechart.min.js"></script>
	<script>
	$(function() {
		var $chart = $('.chart');
		$chart.easyPieChart({
			onStep: function(from, to, percent) {
				$(this.el).find('.percent').text(Math.round(percent));
			}
		});
		var chart = window.chart = $chart.data('easyPieChart');
		chart.update($chart.data('percent'));
		$('.js_update').on('click', function() {
			chart.update(Math.random()*200-100);
		});
	});
	</script>
</body>
</html>
