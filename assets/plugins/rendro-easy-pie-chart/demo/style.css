html,
body {
  padding: 0;
  margin: 0;
}
body {
  -webkit-font-smoothing: antialiased;
  text-align: center;
  font: 18px / 1.4 'Helvetica', 'Arial', sans-serif;
  padding-top: 60px;
}
ul {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
  height: 60px;
  background: #cccccc;
  font-size: 0;
  line-height: 0;
  letter-spacing: -0.3em;
}
li {
  position: relative;
  display: inline-block;
  vertical-align: top;
  width: 25%;
  text-align: center;
  font-size: 18px;
  line-height: 60px;
  letter-spacing: normal;
}
li a {
  display: block;
  color: #ffffff;
  text-decoration: none;
  text-transform: uppercase;
  white-space: nowrap;
}
li a:hover,
li a.active {
  background: #333333;
  color: #ffffff;
}
li a.active:after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -10px;
  border: 10px solid;
  border-color: transparent;
  border-top-color: #333333;
}
.chart {
  position: relative;
  display: inline-block;
  width: 110px;
  height: 110px;
  margin-top: 50px;
  margin-bottom: 50px;
  text-align: center;
}
.chart canvas {
  position: absolute;
  top: 0;
  left: 0;
}
.percent {
  display: inline-block;
  line-height: 110px;
  z-index: 2;
}
.percent:after {
  content: '%';
  margin-left: 0.1em;
  font-size: .8em;
}
.angular {
  margin-top: 100px;
}
.angular .chart {
  margin-top: 0;
}
input {
  display: block;
  margin: auto;
  margin-bottom: 3em;
}
.btn {
  display: block;
  width: 200px;
  margin: 0 auto;
  padding: 10px 20px;
  background: #cccccc;
  color: #ffffff;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 0.8em;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.btn:hover {
  background: #333333;
  color: #ffffff;
}
.span6 {
  float: left;
  width: 50%;
}
@media only screen and (max-width: 600px) {
  li {
    font-size: 14.4px;
  }
}
@media only screen and (max-width: 430px) {
  .span6 {
    float: none;
    width: 100%;
  }
  body {
    padding-top: 0;
  }
  ul {
    height: auto;
    position: static;
  }
  li {
    display: block;
    width: 100%;
  }
  li a {
    white-space: normal;
  }
  a.active:after {
    display: none;
  }
}
