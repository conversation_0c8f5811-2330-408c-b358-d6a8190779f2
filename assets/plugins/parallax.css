.bg-cover {
	background-size: cover;
}
.bg-section {
	color: transparent;
	height: 100%;
	left: 0;
	opacity: 1;
	position: absolute;
	top: 0;
	width: 100%;
	z-index: 0;
	min-height: 400px;
}
.bg-section:after {
	content: "";
	background-color: rgba(0, 0, 0, 0.6);
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
}
/*Slideshow*/

.bg-slideshow {
	margin: 0;
	padding: 0;
}
.bg-slideshow li {
	list-style: none;
}
.parallax-bg {
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
	z-index: -1;
	overflow: hidden;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	z-index: 0;
}
.parallax-bg:after {
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
}
.parallax-light:after {
	background-color: rgba(0, 0, 0, 0.75);
}
.parallax-dark:after {
	/* background-color: rgba(0, 0, 0, 0.85); */
	background-color: rgb(20, 22, 143, 0.85);
}
.parallax-primary:after {
	opacity: 0.9;
}
.no-bg-color-parallax {
	color: #fff;
	background-color: transparent;
	overflow: hidden;
}
.bg-slideshow:after {
	content: '';
}
.bg-slideshow li .bg-slide {
	width: 100%;
	height: 100%;
	top: 0px;
	left: 0px;
	color: transparent;
	background-size: cover;
	background-position: 50% 50%;
	background-repeat: none;
	opacity: 0;
	z-index: 0;
	position: absolute;
}
.parallax-bg .bg-slideshow li .bg-slide {
	-webkit-backface-visibility: hidden;
	-webkit-animation: imageAnimation 18s linear infinite 0s;
	-moz-animation: imageAnimation 18s linear infinite 0s;
	-o-animation: imageAnimation 18s linear infinite 0s;
	-ms-animation: imageAnimation 18s linear infinite 0s;
	animation: imageAnimation 18s linear infinite 0s;
}
.parallax-bg .bg-slideshow li:nth-child(2) .bg-slide {
	-webkit-animation-delay: 9s;
	-moz-animation-delay: 9s;
	-o-animation-delay: 9s;
	-ms-animation-delay: 9s;
	animation-delay: 9s;
}
 @-webkit-keyframes imageAnimation {
 1% {
 opacity: 0;
 -webkit-animation-timing-function: ease-in;
}
 4% {
 opacity: 0;
 -webkit-transform: scale(1);
 -webkit-animation-timing-function: ease-out;
}
 22% {
 opacity: 1;
 -webkit-transform: scale(1.1) rotate(-2deg);
}
 44% {
 opacity: 1;
 -webkit-transform: scale(1.1) rotate(1deg);
}
 100% {
opacity: 0
}
}
 @-moz-keyframes imageAnimation {
 1% {
 opacity: 0;
 -moz-animation-timing-function: ease-in;
}
 4% {
 opacity: 0;
 -moz-transform: scale(1);
 -moz-animation-timing-function: ease-out;
}
 22% {
 opacity: 1;
 -moz-transform: scale(1.1) rotate(-2deg);
}
 44% {
 opacity: 1;
 -moz-transform: scale(1.1) rotate(1deg);
}
 100% {
opacity: 0
}
}
@-o-keyframes imageAnimation {
 1% {
 opacity: 0;
 -o-animation-timing-function: ease-in;
}
 4% {
 opacity: 0;
 -o-transform: scale(1);
 -o-animation-timing-function: ease-out;
}
 22% {
 opacity: 1;
 -o-transform: scale(1.1) rotate(-2deg);
}
 44% {
 opacity: 1;
 -o-transform: scale(1.1) rotate(1deg);
}
 100% {
opacity: 0
}
}
@-ms-keyframes imageAnimation {
 1% {
 opacity: 0;
 -ms-animation-timing-function: ease-in;
}
 4% {
 opacity: 0;
 -ms-transform: scale(1);
 -ms-animation-timing-function: ease-out;
}
 22% {
 opacity: 1;
 -ms-transform: scale(1.1) rotate(-2deg);
}
 44% {
 opacity: 1;
 -ms-transform: scale(1.1) rotate(1deg);
}
 100% {
opacity: 0
}
}
@keyframes imageAnimation {
 1% {
 opacity: 0;
 animation-timing-function: ease-in;
}
 4% {
 opacity: 0;
 transform: scale(1);
 animation-timing-function: ease-out;
}
 22% {
 opacity: 1;
 transform: scale(1.1) rotate(-2deg);
}
 44% {
 opacity: 1;
 transform: scale(1.1) rotate(1deg);
}
 100% {
opacity: 0
}
}
