(function(b){b.fn.formValidation=function(c){option=b.extend({},b.fn.formValidation.defaultOptions,c);this.each(function(){var j=b(this);var F=j.attr("id");var L=false;var y=option;var m="";var l=false;var I=false;var D=false;var e=false;if(y.captchaTheme=="mini"||y.captchaTheme=="mini-dark"||y.captchaTheme=="mini-light"||y.captchaTheme=="mini-greyscale"){l=true}if(y.captchaTheme=="dark"||y.captchaTheme=="mini-dark"){I=true}if(y.captchaTheme=="light"||y.captchaTheme=="mini-light"){ligtTheme=true}if(y.captchaTheme=="greyscale"||y.captchaTheme=="mini-greyscale"){e=true}$("#"+F).attr("action","/sendmail");function d(){var aa="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",X="",Z=[];for(i=1;i<=6;i=i+1){Z[i]=aa.charAt(Math.floor(Math.random()*aa.length));X+=Z[i]}var ac=["#000000","#de2d2d","#888888","#1561b6","#ff6600","#4d961c"],V=[],U;if(y.captchaTheme!=null){switch(y.captchaTheme){case"dark":case"mini-dark":ac=["#9c9c9c","#6e7372","#656565","#5b5b5b","#a4a4a4","#bcbcbc"];break;case"light":case"mini-light":ac=["#cc8e8e","#bb94cd","#7091aa","#568d64","#d38766","#51a991"];break;case"greyscale":case"mini-greyscale":ac=["#818382","#9d9d9d","#fff","#d4d4d4","#cfcdcd","#a4aba9"];break;case"default":case"mini":ac=ac;break}}for(x=1;x<=6;x=x+1){U=ac[Math.floor(Math.random()*ac.length)];V[x]=U}var W=["style1","style2","style3","style4","style5","style6"],Y=[];for(x=1;x<=6;x=x+1){Y[x]=W[Math.floor(Math.random()*W.length)]}var ab=28;if(l){ab=20}function af(ag,ah,ak){switch(ag){case"style1":ah.fillStyle=V[ak];ah.font="Bold "+ab+"px arial";ah.textBaseline="middle";ah.fillText(Z[ak],0,17);break;case"style2":ah.fillStyle=V[ak];ah.font="300 "+ab+"px arial";ah.textBaseline="middle";ah.fillText(Z[ak],0,19);ah.fillStyle=V[3];ah.fillText(Z[ak],0,17);ah.fillStyle=V[ak];ah.fillText(Z[ak],0,17);break;case"style3":ah.rotate(-0.25);ah.fillStyle=V[ak];ah.font="Bold "+ab+"px Comic Sans MS italic ";ah.textBaseline="middle";ah.fillText(Z[ak],0,17);break;case"style4":ah.fillStyle=V[ak];ah.beginPath();ah.moveTo(0,10);ah.lineTo(150,50);ah.font="Bold "+ab+"px arial";ah.textBaseline="middle";ah.fillText(Z[ak],0,17);break;case"style5":ah.fillStyle=V[ak];ah.font="Bold "+ab+"px Times New Roman";ah.lineWidth=2;ah.textBaseline="middle";ah.strokeStyle=V[ak];ah.strokeText(Z[ak],0,17);break;case"style6":if(I||e){var aj=4;var ai=ah.measureText(Z[ak]).width-10+aj*1;ah.shadowColor=V[ak];ah.shadowOffsetX=ai;ah.shadowOffsetY=0;ah.shadowBlur=aj}ah.fillStyle=V[ak];ah.font="Bold "+ab+"px arial";ah.textBaseline="middle";ah.fillText(Z[ak],0,17);break}}var S=document.getElementById(""+F+"_randomtext1");var T=S.getContext("2d");af(Y[1],T,1);var Q=document.getElementById(F+"_randomtext2");var R=Q.getContext("2d");af(Y[2],R,2);var O=document.getElementById(F+"_randomtext3");var P=O.getContext("2d");af(Y[3],P,3);var N=document.getElementById(F+"_randomtext4");var M=N.getContext("2d");af(Y[4],M,4);var s=document.getElementById(F+"_randomtext5");var o=s.getContext("2d");af(Y[5],o,5);var ae=document.getElementById(F+"_randomtext6");var ad=ae.getContext("2d");af(Y[6],ad,6);return X}if(y.enableFlashMessage==true&&$(".flashes").length>0){$(".flashes").show().insertBefore("#"+F)}if(y.enableFlashMessage==false&&$(".flashes").length>0){$(".flashes").hide()}if(y.validateSpam==true){var p=j.find(".spam");if(y.captchaTheme!=null){p.addClass(y.captchaTheme+"-theme")}else{if(y.captchaTheme==null){p.addClass("default-theme")}}for(x=1;x<=6;x=x+1){if(l){p.append('<canvas id="'+F+"_randomtext"+x+'" width="20" height="30" />')}else{p.append('<canvas id="'+F+"_randomtext"+x+'" width="25" height="35" />')}}p.prepend("<div id='randomtext_overlay' ></div>");p.prepend('<span id="refresh_spam" title="Refresh"><i class="fa fa-refresh icon-refresh"></i></span>');$('<input id="spam" type="text" value="">').insertAfter(p);m=d()}var B=j.find("#refresh_spam");B.click(function(){p.find("canvas").remove();for(x=1;x<=6;x=x+1){if(l){p.append('<canvas id="'+F+"_randomtext"+x+'" width="20" height="30" />')}else{p.append('<canvas id="'+F+"_randomtext"+x+'" width="25" height="35" />')}}m=d()});$("#"+F+" :input").focus(function(){if($(this).hasClass("needsfilled")){$(this).val("").removeClass("needsfilled");$("form label").removeClass("needsfilled");$("form select").removeClass("needsfilled2")}});$("form select").focus(function(){if($(this).hasClass("needsfilled2")){$(this).removeClass("needsfilled2")}});$("input[type='radio'], input[type='checkbox']").click(function(){$("form label").removeClass("needsfilled")});$(".resetbutton").click(function(){$("form label").removeClass("needsfilled");$(":input").removeClass("needsfilled");$("form select").removeClass("needsfilled2");$("input[type='text']").val("");$("textarea").val("")});var u=0;if(y.validateText!=false){for(i=0;i<y.validateText.length;i++){u++}}if(y.validateEmail!=false){for(i=0;i<y.validateEmail.length;i++){u++}}if(y.validateRadiobutton!=false){for(i=0;i<y.validateRadiobutton.length;i++){u++}}if(y.validateDropdown!=false){for(i=0;i<y.validateDropdown.length;i++){u++}}if(y.validateCheckbox!=false){for(i=0;i<y.validateCheckbox.length;i++){u++}}if(y.validateLetters!=false){for(i=0;i<y.validateLetters.length;i++){u++}}if(y.validateChar!=false){for(i=0;i<y.validateChar.length;i++){u++}}if(y.validateNum!=false){for(i=0;i<y.validateNum.length;i++){u++}}if(y.validateRange!=false){for(i=0;i<y.validateRange.length;i++){u++}}if(y.validateDomain!=false){for(i=0;i<y.validateDomain.length;i++){u++}}if(y.validateSpam==true){u++}validateTotal=u;var E=$(".error_box[for="+F+"]");var k="";$("#"+F).submit(function(){t();if(L==true||u<=0){f();return true}else{return false}});$("#"+F+" .form-next").click(function(){if(y.validateTab!=false){t();var s=0,o=$(this).attr("data-next");$(this).parent().find(".needsfilled").each(function(){s++});if(s==0){$('button[href="'+o+'"]').removeAttr("disabled");$('[href="'+o+'"]').tab("show")}}});function t(){k="";w(y.validateText);A(y.validateEmail);G(y.validateDropdown);h(y.validateRadiobutton);z(y.validateCheckbox);K(y.validateLetters);J(y.validateChar);C(y.validateNum);n(y.validateRange);v(y.validateDomain);H();if(y.validateStyle!="default"){E.fadeIn({duration:1000,queue:false}).slideDown(1000);E.html(k);E.wrapInner("<span />")}}function f(){if(y.redirectLink!=false){window.open(y.redirectLink,"_blank")}}function H(){if(y.validateSpam!=false){if($("#"+F+" #spam").val()==""||$("#"+F+" #spam").val()=="Required."){if(y.validateStyle=="default"){$("#"+F+" #spam").addClass("needsfilled").val("Required.")}L=false;u=validateTotal;$("#"+F+" #spam").removeClass("correctfilled");k=k+"Captcha input is required.<br>"}else{if(m!=$("#"+F+" #spam").val()){if(y.validateStyle=="default"){$("#"+F+" #spam").addClass("needsfilled").val("Incorrect.")}else{$("#"+F+" #spam").val("")}L=false;u=validateTotal;$("#"+F+" #spam").removeClass("correctfilled");k=k+"Captcha input is not valid.<br>"}else{$("#"+F+" #spam").removeClass("needsfilled").addClass("correctfilled");u--}}}}function w(M){if(y.validateText!=false){for(i=0;i<M.length;i++){var s=$("#"+F+" #"+M[i]);var N=M[i].replace(/_/g," ");var o=N.replace(/^\w/,function(O){return O.toUpperCase()});emptyerror=o+" is required.";if((s.val()=="")||(s.val()==emptyerror)||(s.val().length<2)){if(y.validateStyle=="default"){s.addClass("needsfilled").val(emptyerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{s.removeClass("needsfilled");u--}}}}function A(s){emailerror="Please enter a valid e-mail.";if(y.validateEmail!=false){for(i=0;i<s.length;i++){var N=$("#"+F+" #"+s[i]);var M=s[i].replace(/_/g," ");var o=M.replace(/^\w/,function(O){return O.toUpperCase()});emptyerror=o+" is required.";if(N.val()==""){if(y.validateStyle=="default"){N.addClass("needsfilled").val(emailerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if(!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(N.val())){if(y.validateStyle=="default"){N.addClass("needsfilled").val(emailerror)}else{N.val("")}L=false;u=validateTotal;k=k+o+" input is not a valid e-mail.<br>"}else{N.removeClass("needsfilled");u--}}}}}function G(s){if(y.validateDropdown!=false){for(a=0;a<s.length;a++){var o=$("#"+F+" #"+s[a]);if(($("#"+F+" #"+s[a]).val()=="none")||($("#"+F+" #"+s[a]).val()=="0")||($("#"+F+" #"+s[a]).val()=="")||($("#"+F+" #"+s[a]).val()=="default")){if(y.validateStyle=="default"){$("#"+F+" #"+s[a]).addClass("needsfilled2")}L=false;u=validateTotal;k=k+"Select a "+s[a]+" from the list.<br>"}else{$("#"+F+" #"+s[a]).removeClass("needsfilled2");u--}}}}function h(M){if(y.validateRadiobutton!=false){for(r=0;r<M.length;r++){var P=0;var s=$("#"+F+" #"+M[r]);var O=$("#"+M[r]).find("input:radio").attr("name");var N=document.getElementsByName(O);for(var o=0;o<N.length;o++){if(N[o].checked){P++}}if(P==0){if(y.validateStyle=="default"){$("#"+F+" #"+M[r]+" label").addClass("needsfilled")}L=false;u=validateTotal;k=k+"Select a "+O+".<br>"}else{$("#"+F+" #"+M[r]+" label").removeClass("needsfilled");u--}}}}function z(N){if(y.validateCheckbox!=false){for(r=0;r<N.length;r++){var P=y.validateCheckbox[r].id;var Q=y.validateCheckbox[r].id.replace(/_/g," ");var o=Q.replace(/^\w/,function(R){return R.toUpperCase()});var M=$("#"+F+" #"+P+" input:checkbox");var O=$("#"+F+" #"+P+" input[@type=checkbox]:checked");var s=O.size();if((s<y.validateCheckbox[r].limit[0])||(s>y.validateCheckbox[r].limit[1])){if(y.validateStyle=="default"){$("#"+F+" #"+P+" label").addClass("needsfilled")}L=false;u=validateTotal;if(y.validateCheckbox[r].limit[0]==y.validateCheckbox[r].limit[1]){if(y.validateCheckbox[r].limit[0]==1){k=k+"Select a "+o+" (tick "+y.validateCheckbox[r].limit[0]+" box only).<br>"}else{if(y.validateCheckbox[r].limit[0]>1){k=k+"Select a "+o+" (tick "+y.validateCheckbox[r].limit[0]+" boxes only).<br>"}}}else{k=k+"Select a "+o+" (tick "+y.validateCheckbox[r].limit[0]+"-"+y.validateCheckbox[r].limit[1]+" boxes only).<br>"}}else{$("#"+F+" #"+P+" label").removeClass("needsfilled");u--}}}}function K(N){lettererror="Letters only.";if(y.validateLetter!=false){for(i=0;i<N.length;i++){var s=$("#"+F+" #"+N[i]);var M=N[i].replace(/_/g," ");var o=M.replace(/^\w/,function(O){return O.toUpperCase()});emptyerror=o+" is required.";if((s.val()=="")||(s.val()==emptyerror)){if(y.validateStyle=="default"){s.addClass("needsfilled").val(emptyerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if(!/^[A-Za-z]+$/.test(s.val())){if(y.validateStyle=="default"){s.addClass("needsfilled").val(lettererror)}else{s.val("")}L=false;u=validateTotal;k=k+o+" input should container letters only.<br>"}else{s.removeClass("needsfilled");u--}}}}}function J(M){if(y.validateChar!=false){for(g=0;g<M.length;g++){var O=$("#"+F+" #"+y.validateChar[g].id);var N=y.validateChar[g].id.replace(/_/g," ");var o=N.replace(/^\w/,function(P){return P.toUpperCase()});emptyerror=o+" is required.";var s=5;if(O.val()==""||O.val()==emptyerror){if(y.validateStyle=="default"){O.addClass("needsfilled").val(emptyerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if(y.validateChar[g].min!=null&&y.validateChar[g].max!=null){if(O.val().length<y.validateChar[g].min||O.val().length>y.validateChar[g].max){charerror="Must be "+y.validateChar[g].min+"-"+y.validateChar[g].max+" characters.";k=k+o+" input must be "+y.validateChar[g].min+"-"+y.validateChar[g].max+" characters.<br>";if(y.validateStyle=="default"){O.addClass("needsfilled").val(charerror)}else{O.val("")}L=false;u=validateTotal}else{O.removeClass("needsfilled");u--}}else{if(y.validateChar[g].min!=null&&y.validateChar[g].max==null){if(O.val().length<y.validateChar[g].min){charerror="Must be at least "+y.validateChar[g].min+" characters.";k=k+o+" input must be at least "+y.validateChar[g].min+" character.<br>";if(y.validateStyle=="default"){O.addClass("needsfilled").val(charerror)}else{O.val("")}L=false;u=validateTotal}else{O.removeClass("needsfilled");u--}}else{if(y.validateChar[g].min==null&&y.validateChar[g].max!=null){if(O.val().length>y.validateChar[g].max){charerror="Must be "+y.validateChar[g].max+" characters or less.";k=k+o+" input must be "+y.validateChar[g].max+" characters or less.<br>";if(y.validateStyle=="default"){O.addClass("needsfilled").val(charerror)}else{O.val("")}L=false;u=validateTotal}else{O.removeClass("needsfilled");u--}}}}}}}}function C(N){if(y.validateNum!=false){for(i=0;i<N.length;i++){var s=$("#"+F+" #"+y.validateNum[i].id);var O=y.validateNum[i].id.replace(/_/g," ");var o=O.replace(/^\w/,function(P){return P.toUpperCase()});var M=s.val();numerror=o+" is required.";if(s.val()==""||s.val()==numerror){if(y.validateStyle=="default"){s.addClass("needsfilled").val(numerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if(!/^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(s.val())||(M.length<y.validateNum[i].limit[0]||M.length>y.validateNum[i].limit[1])){if(y.validateNum[i].limit[0]==y.validateNum[i].limit[1]){if(y.validateStyle=="default"){s.addClass("needsfilled").val(y.validateNum[i].limit[0]+" digits only.")}else{s.val("")}k=k+o+" input should contain "+y.validateNum[i].limit[0]+" digits only.<br>"}else{if(y.validateStyle=="default"){s.addClass("needsfilled").val(numerror)}else{s.val("")}}L=false;u=validateTotal}else{s.removeClass("needsfilled");u--}}}}}function n(s){if(y.validateRange!=false){for(q=0;q<s.length;q++){M=y.validateRange[q].limit[0]+"-"+y.validateRange[q].limit[1]+" only.";var N=$("#"+F+" #"+y.validateRange[q].id);var O=y.validateRange[q].id.replace(/_/g," ");var o=O.replace(/^\w/,function(P){return P.toUpperCase()});var M=o+" is required.";if(N.val()==""||N.val()==M){if(y.validateStyle=="default"){N.addClass("needsfilled").val(M)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if((N.val()<y.validateRange[q].limit[0])||(N.val()>y.validateRange[q].limit[1])||(!/^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(N.val()))||N.val()==M){if(y.validateStyle=="default"){N.addClass("needsfilled").val(M)}else{N.val("")}L=false;u=validateTotal;k=k+o+" input should be values "+y.validateRange[q].limit[0]+"-"+y.validateRange[q].limit[1]+" only.<br>"}else{N.removeClass("needsfilled");u--}}}}}function v(M){urlerror="URL is required.";if(y.validateDomain!=false){for(x=0;x<M.length;x++){var s=$("#"+F+" #"+M[x]);var N=M[x].replace(/_/g," ");var o=N.replace(/^\w/,function(O){return O.toUpperCase()});if(s.val()==""||s.val()==urlerror){if(y.validateStyle=="default"){s.addClass("needsfilled").val(urlerror)}L=false;u=validateTotal;k=k+o+" input is required.<br>"}else{if(!/^([a-z][a-z0-9\-]+(\.|\-*\.))+[a-z]{2,6}$/.test(s.val())||(s.val()=="")){if(y.validateStyle=="default"){s.addClass("needsfilled").val("URL format is incorrect.")}else{s.val("")}L=false;u=validateTotal;k=k+o+" is not a valid URL format.<br>"}else{s.removeClass("needsfilled");u--}}}}}})};b.fn.formValidation.stop=function(){return false};b.fn.formValidation.defaultOptions={validateText:false,validateEmail:false,validateRadiobutton:false,validateCheckbox:false,validateDropdown:false,validateSpam:false,validateLetters:false,validateChar:false,validateNum:false,validateRange:false,validateDomain:false,validateTab:false,validateStyle:"default",customFlashMessage:false,redirectLink:false,enableFlashMessage:true,captchaTheme:"default"}})(jQuery);