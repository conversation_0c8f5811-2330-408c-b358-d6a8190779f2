/*
* 	Owl Carousel Owl Demo Theme
*	v1.3.3
*/

.owl-theme.owl-carousel {
	margin-left: -30px;
}

.owl-theme .owl-controls{
	position: absolute;
	bottom: 0;
	right: 0;
	width: 100%;
	text-align: center;
}

/* Styling Next and Prev buttons */

.owl-theme .owl-buttons {
	text-align: right;
	padding-right: 30px;
}
.owl-theme .owl-controls .owl-buttons div {
	position: relative;
	display: inline-block;
	width: 27px;
	height: 30px;
	margin-right: 3px;
	vertical-align: middle;
	color: #FFF;
	transition: all .3s;
}


.owl-theme .owl-controls .owl-buttons div:hover {
	color: #000;
}

/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls.clickable .owl-buttons div:hover{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	text-decoration: none;
}

.owl-theme .owl-controls .owl-buttons div i {
	display: inline-block;
	padding-top: 2px;
	font-style: normal;
}
.owl-theme .owl-controls .owl-buttons .owl-prev i {
	padding-right: 5px;
}
.owl-theme .owl-controls .owl-buttons .owl-next i {
	padding-left: 5px;
}
.owl-theme .owl-controls .owl-buttons .owl-prev i:after,
.owl-theme .owl-controls .owl-buttons .owl-next i:after {
	font-family: FontAwesome;
	font-size: 26px;
}
.owl-theme .owl-controls .owl-buttons .owl-prev i:after {
	content: "\f177";
}
.owl-theme .owl-controls .owl-buttons .owl-next i:after {
	content: "\f178";
}

/* Styling Pagination*/

.owl-theme .owl-controls .owl-page{
	display: inline-block;
	zoom: 1;
	*display: inline;/*IE7 life-saver */
}
.owl-theme .owl-controls .owl-page span{
	display: block;
	margin: 0 4px;
	width: 8px;
	height: 8px;
	border-bottom-left-radius: 5px;
	background-color: #777777;
	box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.1);
}

.owl-theme .owl-controls .owl-page.active span,
.owl-theme .owl-controls.clickable .owl-page:hover span{
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	transition: all .3s;
}

/* If PaginationNumbers is true */

.owl-theme .owl-controls .owl-page span.owl-numbers{
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}

/* preloading images */
.owl-item.loading{
	min-height: 150px;
	background: url(AjaxLoader.gif) no-repeat center center
}
